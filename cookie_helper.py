#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie管理助手
帮助用户获取和管理Lofter登录Cookie
"""

import os
import json
import re
from typing import Optional, Dict


class CookieHelper:
    def __init__(self, cookie_file: str = "lofter_cookies.json"):
        """初始化Cookie助手"""
        self.cookie_file = cookie_file
        self.cookies = self.load_cookies()
    
    def load_cookies(self) -> Dict:
        """加载保存的Cookie"""
        try:
            if os.path.exists(self.cookie_file):
                with open(self.cookie_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载Cookie失败: {e}")
        return {}
    
    def save_cookies(self, cookies: Dict) -> bool:
        """保存Cookie到文件"""
        try:
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存Cookie失败: {e}")
            return False
    
    def parse_cookie_string(self, cookie_string: str) -> Optional[Dict]:
        """解析Cookie字符串"""
        try:
            cookies = {}
            auth_info = {}
            
            # 查找LOFTER登录认证信息
            lofter_match = re.search(r'(LOFTER-[^-]+-LOGIN-AUTH)=([^;]+)', cookie_string)
            if lofter_match:
                auth_key = lofter_match.group(1)
                auth_value = lofter_match.group(2)
                auth_info[auth_key] = auth_value
            
            # 解析所有Cookie
            for cookie_pair in cookie_string.split(';'):
                cookie_pair = cookie_pair.strip()
                if '=' in cookie_pair:
                    key, value = cookie_pair.split('=', 1)
                    cookies[key.strip()] = value.strip()
            
            return {
                'cookies': cookies,
                'auth': auth_info,
                'raw': cookie_string
            }
            
        except Exception as e:
            print(f"解析Cookie失败: {e}")
            return None
    
    def validate_cookie(self, cookie_data: Dict) -> bool:
        """验证Cookie是否有效"""
        if not cookie_data:
            return False
        
        auth = cookie_data.get('auth', {})
        if not auth:
            print("⚠ Cookie中未找到LOFTER登录认证信息")
            return False
        
        cookies = cookie_data.get('cookies', {})
        required_cookies = ['LOFTER-BLOG-SESS', 'LOFTER-SESS']
        
        for required in required_cookies:
            if not any(required in key for key in cookies.keys()):
                print(f"⚠ 缺少必要的Cookie: {required}")
        
        return True
    
    def add_cookie(self, name: str, cookie_string: str) -> bool:
        """添加新的Cookie"""
        cookie_data = self.parse_cookie_string(cookie_string)
        
        if not cookie_data:
            print("Cookie解析失败")
            return False
        
        if not self.validate_cookie(cookie_data):
            print("Cookie验证失败")
            return False
        
        self.cookies[name] = cookie_data
        
        if self.save_cookies(self.cookies):
            print(f"✓ Cookie '{name}' 保存成功")
            return True
        else:
            print("Cookie保存失败")
            return False
    
    def get_cookie(self, name: str) -> Optional[Dict]:
        """获取指定名称的Cookie"""
        return self.cookies.get(name)
    
    def list_cookies(self):
        """列出所有保存的Cookie"""
        if not self.cookies:
            print("没有保存的Cookie")
            return
        
        print("已保存的Cookie:")
        for name, data in self.cookies.items():
            auth_keys = list(data.get('auth', {}).keys())
            print(f"  {name}: {', '.join(auth_keys) if auth_keys else '无认证信息'}")
    
    def delete_cookie(self, name: str) -> bool:
        """删除指定的Cookie"""
        if name in self.cookies:
            del self.cookies[name]
            if self.save_cookies(self.cookies):
                print(f"✓ Cookie '{name}' 删除成功")
                return True
        else:
            print(f"Cookie '{name}' 不存在")
        return False
    
    def interactive_add(self):
        """交互式添加Cookie"""
        print("=== 添加Lofter Cookie ===")
        print("请按以下步骤操作:")
        print("1. 在浏览器中访问: https://newsmiss.lofter.com")
        print("2. 登录您的Lofter账号")
        print("3. 按F12打开开发者工具")
        print("4. 切换到Network(网络)标签页")
        print("5. 刷新页面或进行任意操作")
        print("6. 找到任意请求，点击查看详情")
        print("7. 在Request Headers中找到Cookie行")
        print("8. 复制完整的Cookie值\n")
        
        name = input("请输入Cookie名称 (例如: main_account): ").strip()
        if not name:
            print("名称不能为空")
            return False
        
        cookie_string = input("请粘贴Cookie值: ").strip()
        if not cookie_string:
            print("Cookie不能为空")
            return False
        
        return self.add_cookie(name, cookie_string)


def main():
    """主函数"""
    helper = CookieHelper()
    
    print("=== Lofter Cookie管理助手 ===")
    print("命令:")
    print("1. add - 添加新Cookie")
    print("2. list - 列出所有Cookie")
    print("3. delete - 删除Cookie")
    print("4. quit - 退出")
    print()
    
    while True:
        try:
            command = input("请输入命令: ").strip().lower()
            
            if command == 'quit':
                print("退出程序")
                break
            elif command == 'add':
                helper.interactive_add()
            elif command == 'list':
                helper.list_cookies()
            elif command == 'delete':
                name = input("请输入要删除的Cookie名称: ").strip()
                if name:
                    helper.delete_cookie(name)
            else:
                print("未知命令，请输入 add, list, delete 或 quit")
            
            print()
            
        except KeyboardInterrupt:
            print("\n程序被中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
