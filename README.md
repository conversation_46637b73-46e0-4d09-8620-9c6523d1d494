# Lofter搜索下载器

基于 `fanqie/shareBookSource(4).json` 配置文件的Lofter平台内容搜索和下载工具。

## 功能特性

- 支持多种搜索模式：用户、合集、粮单、文章
- 自动下载文本内容和图片
- 控制台交互界面
- 支持批量下载

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 基本使用

```bash
python lofter_downloader.py
```

### 指定配置文件和输出目录

```bash
python lofter_downloader.py --config fanqie/shareBookSource(4).json --output my_downloads
```

## 搜索格式

1. **搜索用户**: `@用户名`
   - 例如: `@砂上雪`

2. **搜索合集**: `#合集名`
   - 例如: `#百合小说`

3. **搜索粮单**: `%粮单名`
   - 例如: `%双女主`

4. **搜索文章**: 直接输入关键词
   - 例如: `百合`

## 程序流程

1. 启动程序后，输入搜索关键词
2. 程序显示搜索结果列表
3. 选择要下载的项目编号
4. 程序自动下载文本内容和图片到指定目录

## 输出结构

```
downloads/
├── 文章标题.txt          # 文本内容
└── 文章标题_images/      # 图片文件夹
    ├── image_1.jpg
    ├── image_2.jpg
    └── ...
```

## 注意事项

- 需要网络连接访问Lofter API
- 某些内容可能需要登录才能访问
- 请遵守相关网站的使用条款
- 下载的内容仅供个人学习使用

## 配置文件说明

程序基于 `fanqie/shareBookSource(4).json` 中的规则配置：

- `bookSourceUrl`: 基础URL
- `searchUrl`: 搜索URL构建规则
- `ruleSearch`: 搜索结果解析规则
- `ruleContent`: 内容提取规则

## 错误处理

- 网络请求失败会自动重试
- 无效的搜索结果会被跳过
- 文件保存错误会显示错误信息

## 退出程序

在搜索提示符下输入 `quit` 即可退出程序。
