#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lofter下载器测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lofter_downloader import LofterDownloader


def test_config_loading():
    """测试配置文件加载"""
    print("=== 测试配置文件加载 ===")
    try:
        downloader = LofterDownloader("fanqie/shareBookSource(4).json")
        print(f"✓ 配置加载成功")
        print(f"  书源名称: {downloader.config.get('bookSourceName', 'N/A')}")
        print(f"  基础URL: {downloader.base_url}")
        print(f"  请求头: {downloader.headers}")
        return True
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return False


def test_search_url_building():
    """测试搜索URL构建"""
    print("\n=== 测试搜索URL构建 ===")
    try:
        downloader = LofterDownloader("fanqie/shareBookSource(4).json")
        
        test_cases = [
            ("@用户名", "用户搜索"),
            ("#合集名", "合集搜索"),
            ("%粮单名", "粮单搜索"),
            ("普通关键词", "文章搜索")
        ]
        
        for keyword, desc in test_cases:
            url = downloader.build_search_url(keyword, 1)
            print(f"✓ {desc}: {url}")
        
        return True
    except Exception as e:
        print(f"✗ URL构建失败: {e}")
        return False


def test_search_functionality():
    """测试搜索功能"""
    print("\n=== 测试搜索功能 ===")
    try:
        downloader = LofterDownloader("fanqie/shareBookSource(4).json")
        
        # 测试简单搜索
        print("正在测试搜索功能...")
        results = downloader.search("百合", 1)
        
        if results:
            print(f"✓ 搜索成功，找到 {len(results)} 个结果")
            if len(results) > 0:
                first_result = results[0]
                print(f"  第一个结果: {first_result.get('title', 'N/A')}")
        else:
            print("⚠ 搜索返回空结果（可能是网络问题或API变化）")
        
        return True
    except Exception as e:
        print(f"✗ 搜索测试失败: {e}")
        return False


def test_content_extraction():
    """测试内容提取"""
    print("\n=== 测试内容提取 ===")
    try:
        downloader = LofterDownloader("fanqie/shareBookSource(4).json")
        
        # 模拟响应数据
        mock_detail = {
            "response": {
                "post": {
                    "content": "<p>这是一段测试内容</p><br>包含HTML标签",
                    "photoLinks": [
                        {"orign": "https://example.com/image1.jpg"},
                        {"orign": "https://example.com/image2.jpg"}
                    ]
                }
            }
        }
        
        content = downloader.extract_content(mock_detail)
        print(f"✓ 内容提取成功: {content}")
        
        return True
    except Exception as e:
        print(f"✗ 内容提取失败: {e}")
        return False


def test_file_operations():
    """测试文件操作"""
    print("\n=== 测试文件操作 ===")
    try:
        downloader = LofterDownloader("fanqie/shareBookSource(4).json")
        
        # 测试保存内容
        test_content = "这是一段测试内容\n包含中文字符"
        test_filename = "test_file"
        test_dir = "test_downloads"
        
        filepath = downloader.save_content(test_content, test_filename, test_dir)
        
        if filepath and os.path.exists(filepath):
            print(f"✓ 文件保存成功: {filepath}")
            
            # 清理测试文件
            os.remove(filepath)
            if os.path.exists(test_dir) and not os.listdir(test_dir):
                os.rmdir(test_dir)
            print("✓ 测试文件清理完成")
        else:
            print("✗ 文件保存失败")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 文件操作测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("Lofter下载器功能测试")
    print("=" * 50)
    
    tests = [
        test_config_loading,
        test_search_url_building,
        test_content_extraction,
        test_file_operations,
        # test_search_functionality,  # 需要网络连接，可选
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！")
        return True
    else:
        print("✗ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
