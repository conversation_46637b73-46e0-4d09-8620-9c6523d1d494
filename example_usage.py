#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lofter下载器使用示例
"""

from lofter_downloader import LofterDownloader
import json


def example_search_and_download():
    """示例：搜索并下载内容"""
    print("=== Lofter下载器使用示例 ===\n")
    
    # 初始化下载器
    downloader = LofterDownloader("fanqie/shareBookSource(4).json")
    
    # 示例搜索关键词
    search_examples = [
        ("百合", "搜索百合相关文章"),
        ("@砂上雪", "搜索用户"),
        ("#百合小说", "搜索合集"),
        ("%双女主", "搜索粮单")
    ]
    
    for keyword, description in search_examples:
        print(f"🔍 {description}: {keyword}")
        try:
            results = downloader.search(keyword, page=1)
            print(f"   找到 {len(results)} 个结果")
            
            if results:
                # 显示前3个结果
                for i, item in enumerate(results[:3], 1):
                    title = item.get('title', item.get('name', item.get('blogNickName', f'结果{i}')))
                    author = item.get('blogInfo', {}).get('blogNickName', item.get('blogNickName', ''))
                    print(f"   {i}. {title} - {author}")
            
        except Exception as e:
            print(f"   搜索失败: {e}")
        
        print()


def example_batch_download():
    """示例：批量下载"""
    print("=== 批量下载示例 ===\n")
    
    downloader = LofterDownloader("fanqie/shareBookSource(4).json")
    
    # 搜索关键词
    keyword = "百合"
    print(f"搜索关键词: {keyword}")
    
    try:
        results = downloader.search(keyword, page=1)
        print(f"找到 {len(results)} 个结果")
        
        # 下载前5个结果
        download_count = min(5, len(results))
        print(f"准备下载前 {download_count} 个结果...\n")
        
        for i, item in enumerate(results[:download_count], 1):
            print(f"正在下载第 {i} 个项目...")
            
            try:
                # 获取详情
                detail = downloader.get_content_detail(item)
                if detail:
                    # 提取内容
                    content = downloader.extract_content(detail)
                    title = item.get('title', item.get('name', f'content_{i}'))
                    
                    # 清理文件名
                    import re
                    safe_title = re.sub(r'[<>:"/\\|?*]', '_', title)
                    
                    if content:
                        # 保存文本
                        downloader.save_content(content, f"{i}_{safe_title}", "batch_downloads")
                        print(f"   ✓ 文本内容已保存")
                    
                    # 下载图片
                    import os
                    img_dir = os.path.join("batch_downloads", f"{i}_{safe_title}_images")
                    images = downloader.download_images(detail, img_dir)
                    
                    if images:
                        print(f"   ✓ 下载了 {len(images)} 张图片")
                    
                    print(f"   ✓ 第 {i} 个项目下载完成\n")
                else:
                    print(f"   ✗ 第 {i} 个项目获取详情失败\n")
                    
            except Exception as e:
                print(f"   ✗ 第 {i} 个项目下载失败: {e}\n")
        
        print("批量下载完成！")
        
    except Exception as e:
        print(f"批量下载失败: {e}")


def example_config_info():
    """示例：显示配置信息"""
    print("=== 配置信息 ===\n")
    
    try:
        downloader = LofterDownloader("fanqie/shareBookSource(4).json")
        config = downloader.config
        
        print(f"书源名称: {config.get('bookSourceName', 'N/A')}")
        print(f"书源URL: {config.get('bookSourceUrl', 'N/A')}")
        print(f"是否启用: {config.get('enabled', False)}")
        print(f"支持搜索: {bool(config.get('ruleSearch'))}")
        print(f"支持发现: {config.get('enabledExplore', False)}")
        print(f"最后更新: {config.get('lastUpdateTime', 'N/A')}")
        
        # 显示搜索规则
        search_rule = config.get('ruleSearch', {})
        if search_rule:
            print(f"\n搜索规则:")
            print(f"  作者字段: {search_rule.get('author', 'N/A')}")
            print(f"  书名字段: {search_rule.get('name', 'N/A')}")
            print(f"  简介字段: {search_rule.get('intro', 'N/A')}")
            print(f"  封面字段: {search_rule.get('coverUrl', 'N/A')}")
        
    except Exception as e:
        print(f"获取配置信息失败: {e}")


def main():
    """主函数"""
    print("Lofter下载器使用示例\n")
    
    # 显示配置信息
    example_config_info()
    print("\n" + "="*50 + "\n")
    
    # 搜索示例
    example_search_and_download()
    print("="*50 + "\n")
    
    # 询问是否进行批量下载
    choice = input("是否要进行批量下载示例？(y/N): ").strip().lower()
    if choice in ['y', 'yes']:
        example_batch_download()
    else:
        print("跳过批量下载示例")
    
    print("\n示例运行完成！")
    print("要使用交互式下载器，请运行: python lofter_downloader.py")


if __name__ == "__main__":
    main()
