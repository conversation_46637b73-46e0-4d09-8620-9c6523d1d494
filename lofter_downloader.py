#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lofter搜索下载器
基于fanqie/shareBookSource(4).json配置文件
"""

import json
import requests
import re
import os
import time
from urllib.parse import quote, unquote
from typing import Dict, List, Optional, Any
import argparse
from cookie_helper import CookieHelper


class LofterDownloader:
    def __init__(self, config_file: str = "fanqie/shareBookSource(4).json"):
        """初始化下载器"""
        self.session = requests.Session()
        self.config = self.load_config(config_file)
        self.base_url = self.config.get("bookSourceUrl", "https://newsmiss.lofter.com")
        self.login_url = self.config.get("loginUrl", "https://newsmiss.lofter.com")
        self.headers = self.parse_headers()
        self.session.headers.update(self.headers)
        self.login_headers = {}
        self.is_logged_in = False
        self.cookie_helper = CookieHelper()
        
    def load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_list = json.load(f)
                if isinstance(config_list, list) and len(config_list) > 0:
                    return config_list[0]  # 取第一个配置
                return config_list
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}
    
    def parse_headers(self) -> Dict[str, str]:
        """解析请求头"""
        header_str = self.config.get("header", "{}")
        try:
            return json.loads(header_str)
        except:
            return {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"}

    def login_with_cookies(self, cookies: str) -> bool:
        """使用Cookie字符串登录"""
        try:
            # 解析Cookie字符串，查找LOFTER登录认证信息
            import re
            lofter_match = re.search(r';\s*(LOFTER-[^-]+-LOGIN-AUTH)=([^;]+);', cookies)

            if lofter_match:
                auth_key = lofter_match.group(1)
                auth_value = lofter_match.group(2)

                # 设置登录头
                self.login_headers[auth_key] = auth_value
                self.session.headers.update(self.login_headers)

                # 设置Cookie
                cookie_dict = {}
                for cookie_pair in cookies.split(';'):
                    if '=' in cookie_pair:
                        key, value = cookie_pair.strip().split('=', 1)
                        cookie_dict[key] = value

                self.session.cookies.update(cookie_dict)
                self.is_logged_in = True

                print(f"✓ 登录成功，使用认证: {auth_key}")
                return True
            else:
                print("✗ Cookie中未找到LOFTER登录认证信息")
                return False

        except Exception as e:
            print(f"✗ 登录失败: {e}")
            return False

    def login_from_saved(self, cookie_name: str) -> bool:
        """从保存的Cookie登录"""
        cookie_data = self.cookie_helper.get_cookie(cookie_name)
        if not cookie_data:
            print(f"未找到保存的Cookie: {cookie_name}")
            return False

        raw_cookie = cookie_data.get('raw', '')
        if raw_cookie:
            return self.login_with_cookies(raw_cookie)
        else:
            print("Cookie数据无效")
            return False

    def login_interactive(self) -> bool:
        """交互式登录"""
        print("=== Lofter登录 ===")

        # 检查是否有保存的Cookie
        self.cookie_helper.list_cookies()

        choice = input("\n选择登录方式:\n1. 使用保存的Cookie\n2. 输入新的Cookie\n请选择 (1/2): ").strip()

        if choice == '1':
            cookie_name = input("请输入Cookie名称: ").strip()
            if cookie_name:
                return self.login_from_saved(cookie_name)
            else:
                print("未输入Cookie名称")
                return False
        else:
            print("请按以下步骤操作:")
            print("1. 在浏览器中访问: https://newsmiss.lofter.com")
            print("2. 登录您的Lofter账号")
            print("3. 按F12打开开发者工具")
            print("4. 在Network标签页中找到任意请求")
            print("5. 复制请求头中的Cookie值")
            print("6. 将Cookie粘贴到下面\n")

            cookies = input("请输入Cookie: ").strip()

            if cookies:
                success = self.login_with_cookies(cookies)
                if success:
                    # 询问是否保存Cookie
                    save_choice = input("是否保存此Cookie以便下次使用? (y/N): ").strip().lower()
                    if save_choice in ['y', 'yes']:
                        name = input("请输入Cookie名称: ").strip()
                        if name:
                            self.cookie_helper.add_cookie(name, cookies)
                return success
            else:
                print("未输入Cookie")
                return False

    def check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            # 尝试访问需要登录的接口
            test_url = f"{self.base_url}/api/user/info"
            response = self.session.get(test_url)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    self.is_logged_in = True
                    return True

            self.is_logged_in = False
            return False

        except:
            self.is_logged_in = False
            return False
    
    def build_search_url(self, keyword: str, page: int = 1) -> str:
        """构建搜索URL"""
        prefix = keyword[0] if keyword else ""
        offset = (page - 1) * (10 if prefix in ['@', '%'] else 20)
        base_search_url = "https://api.lofter.com/newsearch/"
        
        if prefix == '@':
            # 搜索用户
            return f"{base_search_url}blog.json?key={quote(keyword[1:])}&limit=10&offset={offset}"
        elif prefix in ['#', '＃']:
            # 搜索合集
            return f"{base_search_url}collection.json?key={quote(keyword[1:])}&limit=20&offset={offset}"
        elif prefix == '%':
            # 搜索粮单
            return f"{base_search_url}grain.json?key={quote(keyword[1:])}&limit=10&offset={offset}"
        else:
            # 搜索文章
            headers = {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                "deviceid": "android_device_id",
                "if-modified-since": time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
            }
            return f"{base_search_url}post.json?key={quote(keyword)}&sortType=0&offset={offset}&limit=20"
    
    def search(self, keyword: str, page: int = 1, auto_login: bool = True) -> List[Dict]:
        """搜索内容"""
        try:
            # 检查是否需要登录
            if auto_login and not self.is_logged_in:
                print("⚠ 检测到需要登录，正在尝试交互式登录...")
                if not self.login_interactive():
                    print("⚠ 未登录，某些内容可能无法访问")

            url = self.build_search_url(keyword, page)
            print(f"搜索URL: {url}")

            # 合并登录头信息
            headers = self.session.headers.copy()
            headers.update(self.login_headers)

            response = self.session.get(url, headers=headers)
            response.raise_for_status()

            data = response.json()

            # 检查是否需要登录
            if data.get('code') == 401 or data.get('message') == 'Unauthorized':
                print("⚠ 需要登录才能访问此内容")
                if auto_login and self.login_interactive():
                    # 重新尝试搜索
                    return self.search(keyword, page, auto_login=False)
                else:
                    return []

            # 根据搜索类型提取结果
            if keyword.startswith('@'):
                return data.get('data', {}).get('blogs', [])
            elif keyword.startswith('#') or keyword.startswith('＃'):
                return data.get('data', {}).get('collections', [])
            elif keyword.startswith('%'):
                return data.get('data', {}).get('grainList', [])
            else:
                return data.get('data', {}).get('posts', [])

        except Exception as e:
            print(f"搜索失败: {e}")
            if "401" in str(e) or "Unauthorized" in str(e):
                print("⚠ 可能需要登录才能访问")
            return []
    
    def get_content_detail(self, item: Dict) -> Optional[Dict]:
        """获取内容详情"""
        try:
            if 'postPageUrl' in item:
                # 文章详情
                blog_id = item.get('blogId', '')
                post_id = item.get('id', '')
                body = f"blogdomain=_blogid_{blog_id}.lofter.com&postid={post_id}"
                url = "https://api.lofter.com/oldapi/post/detail.api?product=lofter-android-7.4.4"
                
                response = self.session.post(url, data=body)
                response.raise_for_status()
                return response.json()
            else:
                return item
                
        except Exception as e:
            print(f"获取详情失败: {e}")
            return None
    
    def extract_content(self, detail: Dict) -> str:
        """提取文本内容"""
        try:
            content = detail.get('response', {}).get('post', {}).get('content', '')
            if not content:
                content = detail.get('content', '')
            
            # 清理HTML标签
            content = re.sub(r'<[^>]+>', '', content)
            content = re.sub(r'&[^;]+;', '', content)
            
            return content.strip()
        except:
            return ""
    
    def download_images(self, detail: Dict, save_dir: str) -> List[str]:
        """下载图片"""
        try:
            photo_links = detail.get('response', {}).get('post', {}).get('photoLinks', [])
            if not photo_links:
                return []
            
            os.makedirs(save_dir, exist_ok=True)
            downloaded_files = []
            
            for i, photo in enumerate(photo_links):
                if isinstance(photo, dict) and 'orign' in photo:
                    img_url = photo['orign'].split('%7C')[0]  # 移除参数
                    
                    try:
                        img_response = self.session.get(img_url)
                        img_response.raise_for_status()
                        
                        filename = f"image_{i+1}.jpg"
                        filepath = os.path.join(save_dir, filename)
                        
                        with open(filepath, 'wb') as f:
                            f.write(img_response.content)
                        
                        downloaded_files.append(filepath)
                        print(f"下载图片: {filename}")
                        
                    except Exception as e:
                        print(f"下载图片失败: {e}")
            
            return downloaded_files
            
        except Exception as e:
            print(f"处理图片失败: {e}")
            return []
    
    def save_content(self, content: str, filename: str, save_dir: str = "downloads"):
        """保存内容到文件"""
        try:
            os.makedirs(save_dir, exist_ok=True)
            filepath = os.path.join(save_dir, f"{filename}.txt")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"内容已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"保存文件失败: {e}")
            return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Lofter搜索下载器')
    parser.add_argument('--config', default='fanqie/shareBookSource(4).json', help='配置文件路径')
    parser.add_argument('--output', default='downloads', help='输出目录')
    parser.add_argument('--login', action='store_true', help='启动时进行登录')
    parser.add_argument('--cookies', help='直接使用Cookie登录')
    parser.add_argument('--cookie-name', help='使用保存的Cookie名称登录')

    args = parser.parse_args()

    downloader = LofterDownloader(args.config)

    print("=== Lofter搜索下载器 ===")
    print("⚠ 注意：此书源需要登录才能正常使用")

    # 处理登录
    if args.cookies:
        print("使用提供的Cookie登录...")
        downloader.login_with_cookies(args.cookies)
    elif args.cookie_name:
        print(f"使用保存的Cookie '{args.cookie_name}' 登录...")
        downloader.login_from_saved(args.cookie_name)
    elif args.login:
        print("启动登录流程...")
        downloader.login_interactive()

    print("\n搜索格式说明:")
    print("1. 搜索用户: @用户名")
    print("2. 搜索合集: #合集名")
    print("3. 搜索粮单: %粮单名")
    print("4. 搜索文章: 直接输入关键词")
    print("特殊命令:")
    print("- 输入 'login' 进行登录")
    print("- 输入 'status' 查看登录状态")
    print("- 输入 'quit' 退出程序\n")
    
    while True:
        try:
            keyword = input("请输入搜索关键词: ").strip()

            if keyword.lower() == 'quit':
                print("程序退出")
                break
            elif keyword.lower() == 'login':
                downloader.login_interactive()
                continue
            elif keyword.lower() == 'status':
                if downloader.is_logged_in:
                    print("✓ 已登录")
                else:
                    print("✗ 未登录")
                continue

            if not keyword:
                continue
            
            print(f"\n正在搜索: {keyword}")
            results = downloader.search(keyword)
            
            if not results:
                print("未找到相关内容")
                continue
            
            print(f"\n找到 {len(results)} 个结果:")
            for i, item in enumerate(results, 1):
                title = item.get('title', item.get('name', item.get('blogNickName', f'结果{i}')))
                author = item.get('blogInfo', {}).get('blogNickName', item.get('blogNickName', ''))
                print(f"{i}. {title} - {author}")
            
            choice = input("\n请选择要下载的项目编号 (回车跳过): ").strip()
            
            if choice.isdigit() and 1 <= int(choice) <= len(results):
                selected = results[int(choice) - 1]
                print(f"\n正在获取详情...")
                
                detail = downloader.get_content_detail(selected)
                if detail:
                    content = downloader.extract_content(detail)
                    title = selected.get('title', selected.get('name', f'content_{int(time.time())}'))
                    
                    # 清理文件名
                    safe_title = re.sub(r'[<>:"/\\|?*]', '_', title)
                    
                    if content:
                        downloader.save_content(content, safe_title, args.output)
                    
                    # 下载图片
                    img_dir = os.path.join(args.output, safe_title + "_images")
                    images = downloader.download_images(detail, img_dir)
                    
                    if images:
                        print(f"下载了 {len(images)} 张图片")
                    
                    print("下载完成!")
                else:
                    print("获取详情失败")
            
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n程序被中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    main()
